<script setup lang="ts">
import { ref } from 'vue'

defineProps(['name', 'options'])

const activeIndex = ref(0) // 默认选中第一个（全部）

const handleClick = (index: number) => {
  activeIndex.value = index
}
</script>

<template>
  <div class="content">
    <span>{{ name }}:</span>
    <ul>
      <li :class="{ active: activeIndex === 0 }" @click="handleClick(0)">全部</li>
      <li
        v-for="(option, index) in options"
        :key="option"
        :class="{ active: activeIndex === index + 1 }"
        @click="handleClick(index + 1)"
      >
        {{ option }}
      </li>
    </ul>
  </div>
</template>

<style scoped lang="scss">
.content {
  display: flex;
  align-items: baseline;
  color: #7f7f7f;

  span {
    margin-right: 10px;
    flex-shrink: 0;
  }

  ul {
    padding-left: 0;
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    list-style-type: none;
    user-select: none;

    li {
      cursor: pointer;
      &.active {
        color: #55a6fe;
      }

      &:hover {
        color: #55a6fe;
      }
    }
  }
}
</style>