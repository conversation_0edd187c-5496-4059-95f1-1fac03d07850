import request from '@/utils/request'
import type { HospitalResponseData, HospitalLevenAndRegionResponseData } from './type'

export const getHospitalList = (page: number, limit: number) => {
  return request.get<void, HospitalResponseData>(`/hosp/hospital/${page}/${limit}`)
}

export const getHospitalLevels = (dictCode:string) => {
  return request.get<void, HospitalLevenAndRegionResponseData>('/cmn/dict/findByDictCode/' + dictCode)
}